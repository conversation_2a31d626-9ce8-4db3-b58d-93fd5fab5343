#!/bin/bash

# DigWis Panel 开发环境启动脚本
# 同时监控 Go、Templ 和 CSS 文件的变化

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 设置环境变量
setup_environment() {
    log_info "设置开发环境..."

    # 设置 Go 环境
    export PATH=$HOME/local/go/bin:$HOME/local/node-v20.18.0-linux-arm64/bin:$HOME/local:$PATH
    export GOPATH=$HOME/go

    log_success "环境变量设置完成"
}

# 检查依赖
check_dependencies() {
    log_info "检查开发环境依赖..."

    # 检查 Go
    if ! command -v go &> /dev/null; then
        log_error "Go 未安装或不在 PATH 中"
        exit 1
    fi

    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装或不在 PATH 中"
        exit 1
    fi

    # 检查 npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装或不在 PATH 中"
        exit 1
    fi

    log_success "所有依赖检查通过"
}

# 初始构建
initial_build() {
    log_info "执行初始构建..."
    
    # 生成 Templ 模板
    log_info "生成 Templ 模板..."
    if go run github.com/a-h/templ/cmd/templ@latest generate; then
        log_success "Templ 模板生成完成"
    else
        log_error "Templ 模板生成失败"
        exit 1
    fi
    
    # 构建 CSS
    log_info "构建 CSS..."
    if npm run build-css-prod; then
        log_success "CSS 构建完成"
    else
        log_error "CSS 构建失败"
        exit 1
    fi
}

# 启动文件监控
start_watchers() {
    log_info "启动文件监控..."
    
    # 启动 Templ 监控（后台）
    log_info "启动 Templ 文件监控..."
    go run github.com/a-h/templ/cmd/templ@latest generate --watch &
    TEMPL_PID=$!
    
    # 启动 CSS 监控（后台）
    log_info "启动 CSS 文件监控..."
    npm run watch-css &
    CSS_PID=$!
    
    # 等待一下让监控器启动
    sleep 2
    
    log_success "文件监控启动完成"
    log_info "Templ 监控 PID: $TEMPL_PID"
    log_info "CSS 监控 PID: $CSS_PID"
}

# 启动 Air
start_air() {
    log_info "启动 Air 热重载..."
    
    # 启动 Air
    $HOME/local/air
}

# 清理函数
cleanup() {
    log_warning "正在清理进程..."
    
    # 杀死后台进程
    if [ ! -z "$TEMPL_PID" ]; then
        kill $TEMPL_PID 2>/dev/null || true
        log_info "Templ 监控进程已停止"
    fi
    
    if [ ! -z "$CSS_PID" ]; then
        kill $CSS_PID 2>/dev/null || true
        log_info "CSS 监控进程已停止"
    fi
    
    log_success "清理完成"
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 主函数
main() {
    log_info "🚀 启动 DigWis Panel 开发环境"
    echo "=================================="
    
    check_dependencies
    setup_environment
    initial_build
    start_watchers
    
    log_success "开发环境准备完成！"
    log_info "现在启动 Air..."
    echo "=================================="
    
    start_air
}

# 运行主函数
main "$@"
