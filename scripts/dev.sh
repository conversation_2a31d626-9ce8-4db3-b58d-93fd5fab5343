#!/bin/bash

# DigWis Panel 开发环境启动脚本
# 设置环境变量并启动 Air

set -e

echo "🚀 启动 DigWis Panel 开发环境"
echo "=================================="

# 设置环境变量
export PATH=$HOME/local/go/bin:$HOME/local/node-v20.18.0-linux-arm64/bin:$HOME/local:$PATH
export GOPATH=$HOME/go

echo "✅ 环境变量设置完成"
echo "📁 Go 路径: $(which go)"
echo "📁 Node 路径: $(which node)"
echo "📁 npm 路径: $(which npm)"
echo "=================================="

# 启动 Air
echo "🔥 启动 Air 热重载..."
$HOME/local/air
